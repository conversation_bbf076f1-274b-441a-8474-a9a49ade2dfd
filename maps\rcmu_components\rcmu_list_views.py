"""
RCMU рўйхатлар ва детальлар модули
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from ..models import RcmuData, OutageReason
from .rcmu_permissions import check_rcmu_permission


@login_required
def rcmu_list(request):
    """
    Главная страница РЦМУ - список загруженных данных
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        messages.error(request, 'У вас нет прав доступа к разделу РЦМУ.')
        return redirect('map')
    
    # Получение данных с пагинацией
    rcmu_data = RcmuData.objects.all().order_by('-created_at')
    
    # Фильтрация по региону (если указан)
    region_filter = request.GET.get('region')
    if region_filter:
        rcmu_data = rcmu_data.filter(region__icontains=region_filter)
    
    # Фильтрация по site
    site_filter = request.GET.get('site')
    if site_filter:
        rcmu_data = rcmu_data.filter(site__icontains=site_filter)
    
    # Пагинация
    paginator = Paginator(rcmu_data, 50)  # 50 записей на страницу
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Получение уникальных регионов для фильтра
    regions = RcmuData.objects.values_list('region', flat=True).distinct().order_by('region')
    regions = [r for r in regions if r]  # Убираем пустые значения

    # Получение уникальных филиалов и ответственных для фильтров
    branches = RcmuData.objects.values_list('branch', flat=True).distinct().order_by('branch')
    branches = [b for b in branches if b]

    responsibles = RcmuData.objects.values_list('responsible', flat=True).distinct().order_by('responsible')
    responsibles = [r for r in responsibles if r]
    
    context = {
        'page_obj': page_obj,
        'regions': regions,
        'branches': branches,
        'responsibles': responsibles,
        'current_region': region_filter,
        'current_site': site_filter,
        'total_count': rcmu_data.count(),
    }
    
    return render(request, 'maps/rcmu/list.html', context)


@login_required
def rcmu_treez(request):
    """
    Страница для управления причинами BS Down Zones ≥ 3 count
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        messages.error(request, 'У вас нет прав доступа к разделу РЦМУ.')
        return redirect('map')
    
    # Получение всех причин с сортировкой по дате инцидента
    reasons = OutageReason.objects.all().order_by('-incident_timestamp')
    
    # Фильтрация по району
    area_filter = request.GET.get('area')
    if area_filter:
        reasons = reasons.filter(area_name__icontains=area_filter)
    
    # Фильтрация по региону
    region_filter = request.GET.get('region')
    if region_filter:
        reasons = reasons.filter(region_name__icontains=region_filter)
    
    # Пагинация
    paginator = Paginator(reasons, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Получение уникальных районов и регионов для фильтров
    areas = OutageReason.objects.values_list('area_name', flat=True).distinct().order_by('area_name')
    areas = [a for a in areas if a]
    
    regions = OutageReason.objects.values_list('region_name', flat=True).distinct().order_by('region_name')
    regions = [r for r in regions if r]
    
    context = {
        'page_obj': page_obj,
        'areas': areas,
        'regions': regions,
        'current_area': area_filter,
        'current_region': region_filter,
        'total_count': reasons.count(),
        'LANGUAGE_CODE': request.session.get('language', 'ru'),
    }
    
    return render(request, 'maps/rcmu/treez.html', context)


@login_required
def rcmu_detail(request, pk):
    """
    Получение детальной информации о записи РЦМУ
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа к этой функции.'}, status=403)
    
    rcmu_data = get_object_or_404(RcmuData, pk=pk)
    
    data = {
        'id': rcmu_data.id,
        'branch': rcmu_data.branch,
        'region': rcmu_data.region,
        'date_lego': rcmu_data.date_lego.strftime('%Y-%m-%d') if rcmu_data.date_lego else None,
        'date_performance': rcmu_data.date_performance.strftime('%Y-%m-%d') if rcmu_data.date_performance else None,
        'site': rcmu_data.site,
        'freq': rcmu_data.freq,
        'bs_name': rcmu_data.bs_name,
        'operating_permit': rcmu_data.operating_permit,
        'comments': rcmu_data.comments,
        'cause_ru': rcmu_data.cause_ru,
        'cause_eng': rcmu_data.cause_eng,
        'responsible': rcmu_data.responsible,
        'estimated_air_date': rcmu_data.estimated_air_date,
        'total_down_time_days': rcmu_data.total_down_time_days,
        'quality_comment': rcmu_data.quality_comment,
        'coordinates': rcmu_data.get_coordinates_display(),
        'created_at': rcmu_data.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'created_by': rcmu_data.created_by.username if rcmu_data.created_by else None,
    }
    
    return JsonResponse(data) 