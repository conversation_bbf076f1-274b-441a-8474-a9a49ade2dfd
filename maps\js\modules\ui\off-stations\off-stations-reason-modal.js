// Off Stations причина модали модули
(function() {
    'use strict';
    
    // Конфигурация ва бошқа модулларни олиш
    const { CONFIG } = window.OffStationsConfig;
    const { getTranslations } = window.OffStationsUtils;
    const { saveReasonToServer } = window.OffStationsAPI;
    // Notifications импорти олиб ташланди - энди ишлатилмайди

    // Модални кўрсатиш (notifications олиб ташлангани учун ишламайди)
    function showReasonModal(notificationId) {
        console.warn('showReasonModal: Notifications системаси олиб ташланди. saveReasonFromPopup ишлатинг.');
        return;
    }

    // Модални ёпиш
    function closeReasonModal() {
        const modal = document.getElementById(CONFIG.DOM_IDS.REASON_MODAL);
        if (modal) {
            modal.remove();
        }
    }

    // Причинани сақлаш (notifications олиб ташлангани учун ишламайди)
    function saveReason(notificationId) {
        console.warn('saveReason: Notifications системаси олиб ташланди. saveReasonFromPopup ишлатинг.');
        return;
    }

    // Панелдаги группани причина билан янгилаш
    function updatePanelGroupWithReason(groupId, reason) {
        const translations = getTranslations();

        // Панелдаги барча группаларни кўриб чиқиш
        const groupElements = document.querySelectorAll('.station-group');
        groupElements.forEach(element => {
            // Группа ID ни топиш (координатлар асосида)
            const elementId = element.id;
            if (elementId.includes('station-group')) {
                // data-reason атрибутини қўшиш
                element.setAttribute('data-reason', reason);

                // Причина индикаторини қўшиш
                const locationName = element.querySelector('.location-name');
                if (locationName && !locationName.querySelector('.reason-indicator')) {
                    locationName.insertAdjacentHTML('beforeend',
                        `<i class="fas fa-info-circle reason-indicator" title="${translations.reason_specified}"></i>`);
                }
            }
        });
    }
    
    // Глобал объектга экспорт қилиш
    window.OffStationsReasonModal = {
        showReasonModal: showReasonModal,
        closeReasonModal: closeReasonModal,
        saveReason: saveReason,
        updatePanelGroupWithReason: updatePanelGroupWithReason
    };
    
    // Попап учун глобал функциялар
    window.toggleReasonForm = function(groupId) {
        const form = document.getElementById(`reason-form-${groupId}`);
        const toggleBtn = document.getElementById(`toggle-reason-btn-${groupId}`);
        
        if (form) {
            if (form.style.display === 'none') {
                form.style.display = 'block';
                // Фокусни textarea га қўйиш
                const textarea = document.getElementById(`reason-input-${groupId}`);
                if (textarea) {
                    textarea.focus();
                }
            } else {
                form.style.display = 'none';
            }
        }
    };
    
    window.cancelReasonForm = function(groupId) {
        const form = document.getElementById(`reason-form-${groupId}`);
        if (form) {
            form.style.display = 'none';
        }
    };
    
    window.saveReasonFromPopup = async function(groupId) {
        // Админми текшириш
        const isAdmin = window.userPrivilege && (window.userPrivilege.id === 1 || window.userPrivilege.id === 10);
        
        let reasonToSave, currentLang;
        
        if (isAdmin) {
            // Админлар учун иккала тилдаги причиналарни олиш
            const textareaRu = document.getElementById(`reason-input-ru-${groupId}`);
            const textareaEn = document.getElementById(`reason-input-en-${groupId}`);
            
            if (!textareaRu || !textareaEn) return;
            
            const reasonRu = textareaRu.value.trim();
            const reasonEn = textareaEn.value.trim();
            
            if (!reasonRu && !reasonEn) {
                alert('Пожалуйста, введите причину хотя бы на одном языке');
                return;
            }
            
            // Группа маълумотларини топиш
            let groupData = null;
            for (let [id, group] of window.OffStationsManager.offStationGroups) {
                if (id === groupId) {
                    groupData = group;
                    break;
                }
            }
            
            if (!groupData) {
                console.error('Group data not found');
                return;
            }
            
            // localStorage га сақлаш
            if (reasonRu) {
                localStorage.setItem(`reason_${groupId}_ru`, reasonRu);
            }
            if (reasonEn) {
                localStorage.setItem(`reason_${groupId}_en`, reasonEn);
            }
            
            // Рус тилидаги причинани сақлаш
            if (reasonRu) {
                await window.OffStationsAPI.saveReasonToServer(groupId, reasonRu, groupData);
            }
            
            // Инглиз тилидаги причинани сақлаш
            if (reasonEn) {
                // Вақтинча тилни ўзгартириш
                const tempLang = window.currentLanguage;
                window.currentLanguage = 'en';
                await window.OffStationsAPI.saveReasonToServer(groupId, reasonEn, groupData);
                window.currentLanguage = tempLang;
            }
            
        } else {
            // Оддий фойдаланувчилар учун
            const textarea = document.getElementById(`reason-input-${groupId}`);
            if (!textarea) return;
            
            reasonToSave = textarea.value.trim();
            if (!reasonToSave) {
                alert(window.currentLanguage === 'en' ? 'Please enter a reason' : 'Пожалуйста, введите причину');
                return;
            }
            
            // Группа маълумотларини топиш
            let groupData = null;
            for (let [id, group] of window.OffStationsManager.offStationGroups) {
                if (id === groupId) {
                    groupData = group;
                    break;
                }
            }
            
            if (!groupData) {
                console.error('Group data not found');
                return;
            }
            
            // Текущий язык
            currentLang = window.currentLanguage || 'ru';
            
            // localStorage га сақлаш (тил билан)
            localStorage.setItem(`reason_${groupId}_${currentLang}`, reasonToSave);
            // Умумий reason ҳам сақлаш (fallback учун)
            localStorage.setItem(`reason_${groupId}`, reasonToSave);
            
            // Серверга сақлаш
            await window.OffStationsAPI.saveReasonToServer(groupId, reasonToSave, groupData);
        }
        
        // Попапни қайта очиш
        const popup = document.querySelector('.leaflet-popup');
        if (popup && window.mymap) {
            window.mymap.closePopup();
            
            // groupData ўзгарувчи scope ни тўғирлаш
            let groupData = null;
            for (let [id, group] of window.OffStationsManager.offStationGroups) {
                if (id === groupId) {
                    groupData = group;
                    break;
                }
            }
            
            if (groupData) {
                // Группага қайта учиш ва янгиланган попапни кўрсатиш
                setTimeout(() => {
                    const popupContent = window.createGroupPopupContent(groupData);
                    const newPopup = L.popup({
                        className: 'bs-down-zone-popup',
                        maxWidth: 400,
                        minWidth: 300,
                        autoPan: true
                    })
                        .setLatLng([groupData.center.lat, groupData.center.lon])
                        .setContent(popupContent)
                        .openOn(window.mymap);
                }, 100);
            }
        }
        
        alert(window.currentLanguage === 'en' ? 'Reason saved successfully' : 'Причина успешно сохранена');
    };
})(); 