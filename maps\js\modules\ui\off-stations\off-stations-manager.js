// Off Stations Panel асосий менежер модули
(function () {
    'use strict';

    // Конфигурация ва бошқа модулларни олиш
    const { CONFIG } = window.OffStationsConfig;
    const { createPanelHTML, createGroupHTML, createGroupPopupContent, createEmptyStateHTML, updatePanelContent, updateLanguageElements } = window.OffStationsUI;
    const { clusterOffStations } = window.OffStationsClustering;

    const { showReasonModal, closeReasonModal, saveReason } = window.OffStationsReasonModal;
    const { loadExistingReasons } = window.OffStationsAPI;
    const { generateGroupId } = window.OffStationsUtils;

    // Глобал ўзгарувчилар
    let isPanelVisible = false;
    let isPanelPaused = false;
    let offStationGroups = new Map();
    let previousOffStations = new Set();
    let updateInterval = null;
    let reasonSyncInterval = null;

    // Панелни инициализация қилиш
    function initOffStationsPanel() {
        createPanelHTML();
        createToggleButton();
        attachEventListeners();
        restorePanelState();
        checkOtherModes();

        // Мавжуд причиналарни юклаш
        loadExistingReasons().then(reasons => {
            reasons.forEach(reason => {
                const groupId = reason.group_id;
                // localStorage га сақлаш
                localStorage.setItem(`${CONFIG.STORAGE_KEYS.REASON_PREFIX}${groupId}`, reason.reason);
            });
        });

        updateOffStations();

        // Автоматик янгиланишни бошлаш
        if (updateInterval) {
            clearInterval(updateInterval);
        }
        updateInterval = setInterval(function () {
            if (isPanelVisible && !isPanelPaused) {
                updateOffStations();
            }
        }, CONFIG.UPDATE_INTERVAL);

        // Real-time причина синхронизацияси
        startReasonSyncInterval();
    }

    // Toggle кнопка яратиш
    function createToggleButton() {
        const toggleBtn = document.querySelector('.off-stations-toggle-btn');
        if (toggleBtn) {
            // Тилга қараб title ўрнатиш
            const lang = window.currentLanguage || 'ru';
            toggleBtn.title = lang === 'en' ? 'BS Down Zones ≥ 3 count Panel' : 'Панель зон БС ≥ 3 шт';

            toggleBtn.addEventListener('click', togglePanelVisibility);
        }
    }

    // Event listener лар қўшиш
    function attachEventListeners() {
        // Авария режими ўзгарганда
        window.addEventListener('alarmModeChanged', handleAlarmModeChange);

        // Time Machine checkbox ўзгарганда
        const timeMachineCheckbox = document.getElementById('time-machine-enabled') ||
            document.getElementById('time-machine-checkbox');
        if (timeMachineCheckbox) {
            timeMachineCheckbox.addEventListener('change', handleTimeMachineChange);
        }
    }

    // Панелни кўрсатиш/яшириш
    function togglePanelVisibility() {
        const panel = document.getElementById(CONFIG.DOM_IDS.PANEL);
        const toggleBtn = document.querySelector('.off-stations-toggle-btn');
        const updateIndicator = document.getElementById(CONFIG.DOM_IDS.UPDATE_INDICATOR);

        // Агар кнопка disabled бўлса, ҳеч нарса қилмаймиз
        if (toggleBtn && toggleBtn.disabled) {
            return;
        }

        isPanelVisible = !isPanelVisible;

        if (isPanelVisible) {
            panel.classList.add(CONFIG.CSS_CLASSES.PANEL_VISIBLE);
            toggleBtn.classList.add(CONFIG.CSS_CLASSES.TOGGLE_ACTIVE);
            isPanelPaused = false;
            if (updateIndicator) {
                updateIndicator.classList.remove(CONFIG.CSS_CLASSES.UPDATE_PAUSED);
            }
            // Кўрсатилганда дарҳол янгилаш
            updateOffStations();
            // Real-time синхронизацияни бошлаш
            startReasonSyncInterval();
        } else {
            panel.classList.remove(CONFIG.CSS_CLASSES.PANEL_VISIBLE);
            toggleBtn.classList.remove(CONFIG.CSS_CLASSES.TOGGLE_ACTIVE);
            isPanelPaused = true;
            if (updateIndicator) {
                updateIndicator.classList.add(CONFIG.CSS_CLASSES.UPDATE_PAUSED);
            }
            // Real-time синхронизацияни тўхтатиш
            stopReasonSyncInterval();
        }

        // Ҳолатни сақлаш
        localStorage.setItem(CONFIG.STORAGE_KEYS.PANEL_VISIBLE, isPanelVisible);
    }

    // Панел ҳолатини тиклаш
    function restorePanelState() {
        const savedVisible = localStorage.getItem(CONFIG.STORAGE_KEYS.PANEL_VISIBLE);
        const panel = document.getElementById(CONFIG.DOM_IDS.PANEL);
        const toggleBtn = document.querySelector('.off-stations-toggle-btn');
        const updateIndicator = document.getElementById(CONFIG.DOM_IDS.UPDATE_INDICATOR);

        // Кнопка title ни тилга қараб ўрнатиш
        if (toggleBtn) {
            const lang = window.currentLanguage || 'ru';
            toggleBtn.title = lang === 'en' ? 'BS Down Zones ≥ 3 count Panel' : 'Панель зон БС ≥ 3 шт';
        }

        // Кўринувчанлик ҳолати
        if (savedVisible === 'true') {
            isPanelVisible = true;
            isPanelPaused = false;
            panel.classList.add(CONFIG.CSS_CLASSES.PANEL_VISIBLE);
            if (toggleBtn) {
                toggleBtn.classList.add(CONFIG.CSS_CLASSES.TOGGLE_ACTIVE);
            }
            if (updateIndicator) {
                updateIndicator.classList.remove(CONFIG.CSS_CLASSES.UPDATE_PAUSED);
            }
        } else {
            isPanelVisible = false;
            isPanelPaused = true;
            if (updateIndicator) {
                updateIndicator.classList.add(CONFIG.CSS_CLASSES.UPDATE_PAUSED);
            }
        }
    }

    // Бошқа режимларни текшириш
    function checkOtherModes() {
        const isAlarmMode = window.AlarmManager && window.AlarmManager.isAlarmModeActive && window.AlarmManager.isAlarmModeActive();
        const isTimeMachine = window.isTimeMachineEnabled;
        const shouldDisable = isAlarmMode || isTimeMachine;

        if (shouldDisable) {
            if (isPanelVisible) hidePanel();
            disableToggleButton();
        } else {
            enableToggleButton();
        }
    }

    // Режим ўзгарганда умумий логика
    function handleModeChange(enabled) {
        if (enabled) {
            if (isPanelVisible) hidePanel();
            disableToggleButton();
        } else {
            enableToggleButton();
        }
    }

    // Авария режими ўзгарганда
    function handleAlarmModeChange(event) {
        handleModeChange(event.detail.enabled);
    }

    // Time Machine режими ўзгарганда
    function handleTimeMachineChange(event) {
        handleModeChange(event.target.checked);
    }

    // Toggle button ҳолатини ўзгартириш
    function setToggleButtonState(enabled) {
        const toggleBtn = document.querySelector('.off-stations-toggle-btn');
        if (toggleBtn) {
            toggleBtn.disabled = !enabled;
            toggleBtn.style.opacity = enabled ? '' : '0.5';
            toggleBtn.style.cursor = enabled ? '' : 'not-allowed';
        }
    }

    function disableToggleButton() { setToggleButtonState(false); }
    function enableToggleButton() { setToggleButtonState(true); }

    // Панелни яшириш
    function hidePanel() {
        const panel = document.getElementById(CONFIG.DOM_IDS.PANEL);
        const toggleBtn = document.querySelector('.off-stations-toggle-btn');
        const updateIndicator = document.getElementById(CONFIG.DOM_IDS.UPDATE_INDICATOR);

        if (panel) {
            panel.classList.remove(CONFIG.CSS_CLASSES.PANEL_VISIBLE);
        }
        if (toggleBtn) {
            toggleBtn.classList.remove(CONFIG.CSS_CLASSES.TOGGLE_ACTIVE);
        }
        if (updateIndicator) {
            updateIndicator.classList.add(CONFIG.CSS_CLASSES.UPDATE_PAUSED);
        }

        isPanelVisible = false;
        isPanelPaused = true;

        // Ҳолатни сақлаш
        localStorage.setItem(CONFIG.STORAGE_KEYS.PANEL_VISIBLE, false);
    }

    // Off статусдаги БС ларни янгилаш
    function updateOffStations() {
        if (!canUpdatePanel()) return;

        const offStations = getOffStations();
        const groups = clusterOffStations(offStations);
        updatePanelUI(groups, offStations.length);
    }

    // Панелни янгилаш мумкинлигини текшириш
    function canUpdatePanel() {
        if (!document.getElementById(CONFIG.DOM_IDS.PANEL)) return false;
        if (!isPanelVisible || isPanelPaused) return false;
        if (window.isTimeMachineEnabled || (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled())) return false;
        if (!window.globalStations || !Array.isArray(window.globalStations)) {
            console.warn('Off Stations: globalStations not ready');
            return false;
        }
        return true;
    }

    // Off статусдаги БС ларни олиш
    function getOffStations() {
        return window.globalStations.filter(station => station.status === true);
    }

    // UI ни янгилаш
    function updatePanelUI(groups, totalOffCount) {
        const container = document.getElementById(CONFIG.DOM_IDS.GROUPS_CONTAINER);
        const totalCountEl = document.getElementById(CONFIG.DOM_IDS.TOTAL_COUNT);

        if (!container || !totalCountEl) {
            console.warn('Off Stations Panel elements not found');
            return;
        }

        totalCountEl.textContent = totalOffCount;

        if (groups.length === 0) {
            updatePanelContent(container, createEmptyStateHTML());
            return;
        }

        groups.sort((a, b) => b.stations.length - a.stations.length);
        const html = processGroupsAndGenerateHTML(groups);
        updatePanelContent(container, html);
        attachGroupEventListeners(groups);
    }

    // Группаларни ишлаш ва HTML яратиш
    function processGroupsAndGenerateHTML(groups) {
        let html = '';
        groups.forEach((group, index) => {
            const groupId = generateGroupId(group);
            const isNew = !offStationGroups.has(groupId);

            html += createGroupHTML(group, index, isNew);

            if (isNew) {
                handleNewGroupAutoSave(groupId, group);
            }

            offStationGroups.set(groupId, group);
        });
        return html;
    }

    // Янги группа учун автоматик сақлаш
    function handleNewGroupAutoSave(groupId, group) {
        if (window.OffStationsAPI && window.OffStationsAPI.saveClusterAutomatically) {
            window.OffStationsAPI.saveClusterAutomatically(groupId, group)
                .then(success => {
                    if (success) {
                        console.log(`Кластер автоматик қўшилди: ${groupId}`);
                    }
                })
                .catch(error => {
                    console.error('Автоматик кластер қўшишда хато:', error);
                });
        }
    }

    // Группа event listener ларини қўшиш
    function attachGroupEventListeners(groups) {
        groups.forEach((group, index) => {
            const element = document.getElementById(`station-group-${index}`);
            if (element) {
                element.addEventListener('click', () => flyToGroup(group));
                attachReasonPopupEvents(element, group);
            }
        });
    }

    // Причина попап event ларини қўшиш
    function attachReasonPopupEvents(element, group) {
        const reason = element.getAttribute('data-reason');
        if (reason) {
            const groupId = window.OffStationsUtils.generateGroupId(group);

            element.addEventListener('mouseenter', () => {
                if (window.OffStationsReasonPopup) {
                    window.OffStationsReasonPopup.showReasonPopup(groupId, reason, element);
                }
            });

            element.addEventListener('mouseleave', () => {
                if (window.OffStationsReasonPopup) {
                    window.OffStationsReasonPopup.scheduleHidePopup();
                }
            });
        }
    }

    // Группага учиш
    function flyToGroup(group) {
        if (window.mymap) {
            window.mymap.flyTo([group.center.lat, group.center.lon], 14, {
                duration: 1.5
            });

            // Попап фақат рухсатли фойдаланувчилар учун кўрсатиш
            const { checkRcmuPermission } = window.OffStationsAPI;
            if (checkRcmuPermission()) {
                const popupContent = createGroupPopupContent(group);
                const popup = L.popup({
                    className: 'bs-down-zone-popup',
                    maxWidth: 400,
                    minWidth: 300,
                    autoPan: true,
                    autoPanPaddingTopLeft: [10, 60],
                    autoPanPaddingBottomRight: [10, 10]
                })
                    .setLatLng([group.center.lat, group.center.lon])
                    .setContent(popupContent)
                    .openOn(window.mymap);
            }
            // Бошқа фойдаланувчилар учун фақат картага учиш, попап йўқ
        }
    }

    // Мавжуд группани топиш (координатлар бўйича)
    function findExistingGroup(newGroup) {
        const tolerance = CONFIG.COORDINATE_TOLERANCE;

        for (let [groupId, existingGroup] of offStationGroups) {
            const latDiff = Math.abs(existingGroup.center.lat - newGroup.center.lat);
            const lonDiff = Math.abs(existingGroup.center.lon - newGroup.center.lon);

            // Агар координатлар яқин бўлса ва БС лар сони бир хил бўлса
            if (latDiff < tolerance && lonDiff < tolerance &&
                existingGroup.stations.length === newGroup.stations.length) {
                return existingGroup;
            }
        }
        return null;
    }

    // Тил ўзгарганда панелни янгилаш
    function updateOffStationsPanelLanguage() {
        // Агар панел ҳали яратилмаган бўлса, чиқиб кетамиз
        if (!document.getElementById(CONFIG.DOM_IDS.PANEL)) {
            return;
        }

        updateLanguageElements();

        // Reason popup тилини янгилаш
        if (window.OffStationsReasonPopup && window.OffStationsReasonPopup.updateLanguage) {
            window.OffStationsReasonPopup.updateLanguage();
        }

        // Маълумотларни қайта янгилаш
        updateOffStations();
    }

    // Real-time причина синхронизацияси
    function startReasonSyncInterval() {
        if (reasonSyncInterval) clearInterval(reasonSyncInterval);

        reasonSyncInterval = setInterval(() => {
            if (window.OffStationsAPI && window.OffStationsAPI.syncReasonsRealTime) {
                window.OffStationsAPI.syncReasonsRealTime()
                    .catch(error => console.error('Real-time причина синхронизацияси хатоси:', error));
            }
        }, 15000);
    }

    function stopReasonSyncInterval() {
        if (reasonSyncInterval) {
            clearInterval(reasonSyncInterval);
            reasonSyncInterval = null;
        }
    }

    // Глобал интерфейс
    window.initOffStationsPanel = initOffStationsPanel;
    window.updateOffStations = updateOffStations;
    window.updateOffStationsPanelLanguage = updateOffStationsPanelLanguage;

    // Публичный API причина учун
    window.OffStationsPanel = {
        showReasonModal: showReasonModal,
        closeReasonModal: closeReasonModal,
        saveReason: saveReason
    };

    // OffStationsManager объектини глобал қилиш
    window.OffStationsManager = {
        offStationGroups: offStationGroups,
        updateOffStations: updateOffStations
    };

    // Export для использования в других модулях
    window.createGroupPopupContent = createGroupPopupContent;
})(); 