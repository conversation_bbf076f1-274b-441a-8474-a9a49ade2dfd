{% extends "base.html" %}
{% load static %}

{% block title %}BS Down Zones ≥ 3 count - Причины{% endblock %}

{% block extra_css %}
<style>
    .bs-down-container {
        padding: 20px;
    }
    
    .bs-down-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .bs-down-filters {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .filter-group label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
    }
    
    .reasons-table {
        width: 100%;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .reasons-table thead {
        background: #007bff;
        color: white;
    }
    
    .reasons-table th,
    .reasons-table td {
        padding: 12px 16px;
        text-align: left;
    }
    
    .reasons-table tbody tr {
        border-bottom: 1px solid #dee2e6;
        transition: background-color 0.2s;
    }
    
    .reasons-table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .reason-text {
        max-width: 400px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .station-count {
        display: inline-block;
        padding: 4px 8px;
        background: #e9ecef;
        border-radius: 4px;
        font-weight: 500;
    }
    
    .location-info {
        color: #6c757d;
        font-size: 14px;
    }
    
    .timestamp {
        color: #6c757d;
        font-size: 13px;
    }
    
    .actions {
        display: flex;
        gap: 10px;
    }
    
    .btn-sm {
        padding: 4px 12px;
        font-size: 13px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }
    
    .add-reason-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        align-items: center;
        justify-content: center;
    }
    
    .add-reason-modal.show {
        display: flex;
    }
    
    .modal-content {
        background: white;
        border-radius: 8px;
        padding: 30px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .modal-header h3 {
        margin: 0;
    }
    
    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }
    
    .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
    
    textarea.form-control {
        min-height: 100px;
        resize: vertical;
    }
    
    .modal-footer {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="bs-down-container">
    <div class="bs-down-header">
        <div>
            <h2>BS Down Zones ≥ 3 count - Причины</h2>
            <p class="text-muted mb-0">Управление причинами аварий кластеров</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="window.location.href='/map/'">
                <i class="fas fa-map"></i> К карте
            </button>
        </div>
    </div>
    
    <div class="bs-down-filters">
        <div class="filter-group">
            <label>Район</label>
            <select class="form-select" id="area-filter" onchange="applyFilters()">
                <option value="">Все районы</option>
                {% for area in areas %}
                    <option value="{{ area }}" {% if current_area == area %}selected{% endif %}>{{ area }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div class="filter-group">
            <label>Регион</label>
            <select class="form-select" id="region-filter" onchange="applyFilters()">
                <option value="">Все регионы</option>
                {% for region in regions %}
                    <option value="{{ region }}" {% if current_region == region %}selected{% endif %}>{{ region }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div class="filter-group" style="margin-left: auto">
            <label>&nbsp;</label>
            <button class="btn btn-success" onclick="refreshBSDownClusters()">
                <i class="fas fa-sync"></i> Обновить кластеры
            </button>
        </div>
    </div>
    
    {% if page_obj.object_list %}
    <table class="reasons-table">
        <thead>
            <tr>
                <th>Время инцидента</th>
                <th>Местоположение</th>
                <th>Кол-во БС</th>
                <th>Причина</th>
                <th>Добавил</th>
                <th>Действия</th>
            </tr>
        </thead>
        <tbody>
            {% for reason in page_obj.object_list %}
            <tr data-group-id="{{ reason.group_id }}">
                <td class="timestamp">{{ reason.incident_timestamp|date:"d.m.Y H:i" }}</td>
                <td class="location-info">{{ reason.get_location_display }}</td>
                <td><span class="station-count">{{ reason.stations_count }} БС</span></td>
                <td class="reason-text" title="{% if LANGUAGE_CODE == 'en' %}{{ reason.reason_en|default:reason.reason }}{% else %}{{ reason.reason_ru|default:reason.reason }}{% endif %}">
                    {% if LANGUAGE_CODE == 'en' %}{{ reason.reason_en|default:reason.reason }}{% else %}{{ reason.reason_ru|default:reason.reason }}{% endif %}
                </td>
                <td>{{ reason.created_by.username|default:"-" }}</td>
                <td class="actions">
                    <button class="btn btn-sm btn-info" onclick="viewOnMap('{{ reason.group_id }}')">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editReason({{ reason.id }}, '{{ reason.reason_ru|default:reason.reason|escapejs }}', '{{ reason.reason_en|escapejs }}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    {% if user.privilege_id == 1 or user.privilege_id == 6 %}
                    <button class="btn btn-sm btn-danger" onclick="deleteReason({{ reason.id }})">
                        <i class="fas fa-trash"></i>
                    </button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% include "pagination.html" %}
    
    {% else %}
    <div class="empty-state">
        <i class="fas fa-clipboard-list"></i>
        <h4>Нет сохраненных причин</h4>
        <p>Причины будут появляться здесь после их добавления для BS Down кластеров</p>
    </div>
    {% endif %}
</div>

<!-- Модальное окно для редактирования причины -->
<div class="add-reason-modal" id="edit-reason-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Редактировать причину</h3>
            <button class="close-btn" onclick="closeEditModal()">&times;</button>
        </div>
        <form id="edit-reason-form">
            <input type="hidden" id="edit-reason-id">
            <div class="form-group">
                <label>Причина аварии</label>
                <textarea class="form-control" id="edit-reason-text" required></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Отмена</button>
                <button type="submit" class="btn btn-primary">Сохранить</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Применение фильтров
function applyFilters() {
    const area = document.getElementById('area-filter').value;
    const region = document.getElementById('region-filter').value;
    
    let url = new URL(window.location.href);
    
    if (area) {
        url.searchParams.set('area', area);
    } else {
        url.searchParams.delete('area');
    }
    
    if (region) {
        url.searchParams.set('region', region);
    } else {
        url.searchParams.delete('region');
    }
    
    window.location.href = url.toString();
}

// Обновление BS Down кластеров
function refreshBSDownClusters() {
    // Переход на карту с автоматическим открытием панели BS Down
    window.location.href = '/map/?open_bs_down=true';
}

// Просмотр на карте
function viewOnMap(groupId) {
    // Переход на карту с выделением конкретной группы
    window.location.href = '/map/?highlight_group=' + groupId;
}

// Редактирование причины
function editReason(reasonId, reasonRu, reasonEn) {
    const isAdmin = {% if user.privilege_id == 1 or user.privilege_id == 6 %}true{% else %}false{% endif %};
    const currentLang = window.currentLanguage || 'ru';
    
    document.getElementById('edit-reason-id').value = reasonId;
    
    // Админлар учун иккала тилдаги инпутлар кўрсатилиши керак
    if (isAdmin) {
        const modalBody = document.querySelector('#edit-reason-modal .form-group');
        modalBody.innerHTML = `
            <label>Причина аварии (рус)</label>
            <textarea class="form-control" id="edit-reason-text-ru" required>${reasonRu || ''}</textarea>
            <label style="margin-top: 10px;">Reason (eng)</label>
            <textarea class="form-control" id="edit-reason-text-en" required>${reasonEn || ''}</textarea>
        `;
    } else {
        // Оддий фойдаланувчилар учун фақат жорий тилдаги причина
        const reasonText = currentLang === 'en' ? (reasonEn || reasonRu) : reasonRu;
        document.getElementById('edit-reason-text').value = reasonText;
    }
    
    document.getElementById('edit-reason-modal').classList.add('show');
}

// Закрытие модального окна
function closeEditModal() {
    document.getElementById('edit-reason-modal').classList.remove('show');
}

// Удаление причины
async function deleteReason(reasonId) {
    if (!confirm('Вы уверены, что хотите удалить эту причину?')) {
        return;
    }
    
    try {
        const response = await fetch('/map/rcmu/api/delete-reason/' + reasonId + '/', {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Причина успешно удалена');
            window.location.reload();
        } else {
            alert('Ошибка: ' + data.error);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Произошла ошибка при удалении');
    }
}

// Обработка формы редактирования
document.getElementById('edit-reason-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const reasonId = document.getElementById('edit-reason-id').value;
    const isAdmin = {% if user.privilege_id == 1 or user.privilege_id == 6 %}true{% else %}false{% endif %};
    
    try {
        if (isAdmin) {
            // Админлар учун иккала тилдаги причиналарни янгилаш
            const reasonRu = document.getElementById('edit-reason-text-ru').value.trim();
            const reasonEn = document.getElementById('edit-reason-text-en').value.trim();
            
            if (!reasonRu && !reasonEn) {
                alert('Пожалуйста, введите причину хотя бы на одном языке');
                return;
            }
            
            // Рус тилидаги причинани янгилаш
            if (reasonRu) {
                await fetch('/map/rcmu/api/update-reason/' + reasonId + '/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        reason: reasonRu,
                        lang: 'ru'
                    })
                });
            }
            
            // Инглиз тилидаги причинани янгилаш
            if (reasonEn) {
                await fetch('/map/rcmu/api/update-reason/' + reasonId + '/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        reason: reasonEn,
                        lang: 'en'
                    })
                });
            }
            
            alert('Причина успешно обновлена');
            window.location.reload();
            
        } else {
            // Оддий фойдаланувчилар учун
            const reasonText = document.getElementById('edit-reason-text').value;
            
            if (!reasonText.trim()) {
                alert('Пожалуйста, введите причину');
                return;
            }
            
            const currentLang = window.currentLanguage || 'ru';
            
            const response = await fetch('/map/rcmu/api/update-reason/' + reasonId + '/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    reason: reasonText,
                    lang: currentLang
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('Причина успешно обновлена');
                window.location.reload();
            } else {
                alert('Ошибка: ' + data.error);
            }
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Произошла ошибка при обновлении');
    }
    
    closeEditModal();
});

// Получение CSRF токена
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %} 