"""
API эндпоинтлар модули
"""
from datetime import datetime
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.utils.dateparse import parse_datetime
from bsview.models import RegionUzb, AreaUzb, Current_Alarms, Log_Alarms
from ..map_data_utils import get_bs_data_with_alarms, prepare_map_data_response, get_technology_from_alarm


@api_view(['GET'])
def get_map_data(request):
    """API для получения данных карты"""
    try:
        # Получаем параметр времени если есть
        datetime_param = request.query_params.get('datetime')
        
        # Для отладки
        if datetime_param:
            print(f"[DEBUG] get_map_data: datetime_param = {datetime_param}")
        
        # Умумий функция орқали БС ва авария маълумотларини олиш
        bs_stations, regions = get_bs_data_with_alarms(datetime_param)

        # Для отладки - считаем БС с авариями
        stations_with_alarms = [s for s in bs_stations if s.get('status', False) is True]
        print(f"[DEBUG] get_map_data: Total stations = {len(bs_stations)}, With alarms = {len(stations_with_alarms)}")

        # Формируем ответ
        response_data = prepare_map_data_response(bs_stations, regions)
        
        return Response(response_data)
    except Exception as e:
        print(f"[ERROR] get_map_data: {str(e)}")
        print(f"[ERROR] Exception type: {type(e).__name__}")
        import traceback
        print(f"[ERROR] Traceback:\n{traceback.format_exc()}")
        return Response({"error": str(e), "type": type(e).__name__}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_all_regions(request):
    """Barcha viloyatlarni olish"""
    regions = RegionUzb.objects.all().values('id', 'name')
    return Response(list(regions))


@api_view(['GET'])
def get_all_areas(request):
    """Barcha tumanlarni olish"""
    areas = AreaUzb.objects.all().values('id', 'name', 'region_id')
    return Response(list(areas))


@api_view(['GET'])
def get_areas_by_region(request, region_id):
    """Viloyat bo'yicha tumanlarni olish"""
    areas = AreaUzb.objects.filter(region_id=region_id).values('id', 'name', 'region_id')
    return Response(list(areas))


@api_view(['GET'])
def get_historical_alarms(request):
    """Танланган вақт учун тарихий аварияларни олиш"""
    try:
        # Вақт параметрини олиш
        datetime_str = request.query_params.get('datetime')
        if not datetime_str:
            return Response({"error": "datetime parameter is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Вақтни парс қилиш
        try:
            # Аввал ISO форматни синаб кўрамиз
            target_datetime = parse_datetime(datetime_str)
            if target_datetime and target_datetime.tzinfo:
                # Агар timezone маълумоти бор бўлса, уни олиб ташлаймиз
                target_datetime = target_datetime.replace(tzinfo=None)
            elif not target_datetime:
                # Агар ISO формат ишламаса, бошқа форматларни синаб кўрамиз
                target_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return Response({"error": "Invalid datetime format. Use ISO format or YYYY-MM-DD HH:MM:SS"},
                          status=status.HTTP_400_BAD_REQUEST)

        # Танланган вақт авария давомийлиги ичида бўлган аварияларни топиш
        historical_alarms = Log_Alarms.objects.filter(
            appeartime__lte=target_datetime,  # Авария танланган вақтдан олдин ёки ўша вақтда бошланган
            cleartime__gte=target_datetime    # Авария танланган вақтда ёки кейин тугаган
        ).values('bsname', 'bsnumber', 'alarmname', 'appeartime', 'cleartime')

        # Натижаларни форматлаш
        result = []
        for alarm in historical_alarms:
            technology = get_technology_from_alarm(alarm['alarmname'])
            if technology:  # Фақат 2G/3G/4G аварияларини қайтариш
                # Авария давомийлигини ҳисоблаш
                appear_time = alarm['appeartime']

                # Танланган вақтгача бўлган давомийликни ҳисоблаш
                duration = target_datetime - appear_time
                duration_hours = duration.total_seconds() / 3600

                # Фақат мусбат давомийликни қабул қилиш
                if duration_hours > 0:
                    result.append({
                        'bsname': alarm['bsname'],
                        'bsnumber': alarm['bsnumber'],
                        'alarmname': alarm['alarmname'],
                        'technology': technology,
                        'appeartime': alarm['appeartime'],
                        'cleartime': alarm['cleartime'],
                        'duration_hours': round(duration_hours, 2)
                    })

        return Response(result)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 