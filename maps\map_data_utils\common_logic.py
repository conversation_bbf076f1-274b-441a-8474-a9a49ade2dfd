"""
БС ва авария маълумотларини олиш учун умумий логика
"""
from bsview.models import RegionUzb, Current_Alarms
from .utils import getNumberBS, get_cabinet_type
from .bs_handlers import get_formatted_bs_data


def get_bs_data_with_alarms(datetime_param=None):
    """
    БС маълумотларини авария маълумотлари билан олиш
    
    Args:
        datetime_param: Тарихий маълумотлар учун вақт параметри
        
    Returns:
        tuple: (bs_stations, regions) - БС маълумотлари ва регионлар рўйхати
    """
    # БС маълумотларини олиш
    if datetime_param:
        # Тарихий маълумотлар учун
        bs_stations = get_formatted_bs_data(datetime_param=datetime_param, skip_alarms=False)
    else:
        # Жорий маълумотлар учун
        bs_stations = get_formatted_bs_data(skip_alarms=True)
        
        # БС лар ва аларм луғати
        bs_by_number = {}
        alarm_details = {}
        
        # БС маълумотларини номер бўйича луғатга жойлаш
        for bs in bs_stations:
            bsnum = getNumberBS(bs['name'])
            if bsnum:
                bs_by_number[bsnum] = bs['id']
        
        # Барча аларм маълумотларини йиғиш
        for alarm in Current_Alarms.objects.all():
            bs_number = getNumberBS(alarm.bsname)
            if not bs_number or bs_number not in bs_by_number:
                continue

            bs_id = bs_by_number[bs_number]

            # Технология турини аниқлаш
            if alarm.bscrnc.startswith("BSC"):
                typeG = "2G"
                alarm_name = "OML Fault"
            elif alarm.bscrnc.startswith("RNC"):
                typeG = "3G"
                alarm_name = "NodeB Unavailable"
            elif alarm.bscrnc.startswith("LTE"):
                typeG = "4G"
                alarm_name = "S1ap Link Down"
            else:
                continue

            # Илк марта кўрилган БС алармини қўшиш
            if bs_id not in alarm_details:
                alarm_details[bs_id] = {
                    'status': True,
                    'calcTime': str(alarm.calctime) if hasattr(alarm, 'calctime') else None,
                    'typeG': typeG,
                    'cabinetType': get_cabinet_type(alarm.bsname),
                    'alarms_by_tech': {}
                }

            # Технология бўйича авария маълумотларини қўшиш
            alarm_details[bs_id]['alarms_by_tech'][typeG] = {
                'alarmname': alarm_name,
                'appeartime': alarm.appeartime.isoformat() if alarm.appeartime else None,
                'cleartime': None,  # Жорий авария ҳали тугамаган
                'duration': str(alarm.calctime) if hasattr(alarm, 'calctime') else None
            }

            # Мавжуд БС га технология турини қўшиш
            if typeG not in alarm_details[bs_id]['typeG']:
                alarm_details[bs_id]['typeG'] += "/" + typeG
        
        # Аваряли БС ларга статус қўшиш
        for bs in bs_stations:
            if bs['id'] in alarm_details:
                bs.update(alarm_details[bs['id']])
    
    # Регионлар рўйхатини олиш
    regions = list(RegionUzb.objects.all().values('id', 'name'))
    
    return bs_stations, regions


def prepare_map_data_response(bs_stations, regions):
    """
    Карта маълумотларини жавоб форматига тайёрлаш
    
    Args:
        bs_stations: БС маълумотлари рўйхати
        regions: Регионлар рўйхати
        
    Returns:
        dict: Форматланган жавоб маълумотлари
    """
    return {
        'points': bs_stations,
        'regions': regions
    }
