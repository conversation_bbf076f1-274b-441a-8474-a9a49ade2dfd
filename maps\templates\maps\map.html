{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} QNP Map Monitoring {% endblock title %}

{% block content %}

<!-- Внешние библиотеки -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<!-- Leaflet MarkerCluster CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet-search/4.0.0/leaflet-search.min.css" />

<!-- Модульные CSS файлы -->
<link rel="stylesheet" href="/maps/css/main.css?v=1.3">
<link rel="stylesheet" href="/maps/css/loader.css?v=1.3">
<link rel="stylesheet" href="/maps/css/sidebar.css?v=1.3">
<link rel="stylesheet" href="/maps/css/map-controls.css?v=1.8">
<link rel="stylesheet" href="/maps/css/legend.css?v=1.3">
<link rel="stylesheet" href="/maps/css/popup.css?v=1.7">
<link rel="stylesheet" href="/maps/css/regions-table.css?v=1.3">
<link rel="stylesheet" href="/maps/css/time-machine.css?v=1.6">
<link rel="stylesheet" href="/maps/css/cluster.css?v=1.0">
<link rel="stylesheet" href="/maps/css/alarms.css?v=1.0">
<link rel="stylesheet" href="/maps/css/off-stations-panel.css?v=1.2">

<!-- Внешние скрипты - ПРАВИЛЬНЫЙ ПОРЯДОК -->
<!-- 1. Сначала Leaflet -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<!-- 2. Leaflet MarkerCluster -->
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet-search/4.0.0/leaflet-search.min.js"></script>

<!-- 2. Затем proj4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/proj4js/2.7.5/proj4.js"></script>

<!-- 3. И только потом proj4leaflet -->
<script src="https://unpkg.com/proj4leaflet@1.0.2/src/proj4leaflet.js"></script>

<!-- Яндекс Карта API -->
<script src="https://api-maps.yandex.ru/2.1/?apikey=a14b0e94-914e-40ce-a166-f32fd55744cb&lang=ru_RU"></script>

<!-- Loader компонент -->
{% include 'maps/components/loader.html' %}

<div class="container-fluid">
    <!-- Sidebar компонент -->
    {% include 'maps/components/sidebar.html' %}
</div>

<!-- Карта контейнер -->
<div id="mapid">
    <!-- Time Machine toggle кнопкаси - карта ичида -->
    <div class="time-machine-toggle-btn">
        <button class="time-machine-btn" id="time-machine-btn">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path
                    d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z" />
            </svg>
        </button>
    </div>

    <!-- Time Machine контейнери - карта ичида -->
    <div class="time-machine-floating-container" id="time-machine-floating-container">
        <div class="time-machine-body">
            <input type="checkbox" id="time-machine-enabled" style="display: none;" checked>
            <div class="time-machine-controls" id="time-machine-controls">
                <!-- Бир қаторда слайдер ва вақт танлаш -->
                <div class="time-slider-with-inputs">
                    <button id="time-slider-left" class="time-slider-arrow" title="Назад">◀</button>
                    <div class="time-slider-track-horizontal">
                        <input type="range" id="time-slider" min="0" max="1440" value="720" step="5">
                        <div class="time-slider-labels">
                            <span class="time-label">00:00</span>
                            <span class="time-label">06:00</span>
                            <span class="time-label">12:00</span>
                            <span class="time-label">18:00</span>
                            <span class="time-label">24:00</span>
                        </div>
                    </div>
                    <button id="time-slider-right" class="time-slider-arrow" title="Вперед">▶</button>

                    <!-- Вақт киритиш элементлари -->
                    <div class="time-inputs-inline">
                        <input type="text" id="time-machine-date" class="form-control" title="Дата" autocomplete="off">
                        <select id="time-machine-hour" title="Час"></select>
                        <span class="time-separator">:</span>
                        <select id="time-machine-minute" title="Минута"></select>
                    </div>
                </div>

                <!-- Навигация тугмалари -->
                <div class="time-navigation">
                    <button id="time-back-hour" class="time-nav-btn" title="-1 соат">-1ч</button>
                    <button id="time-back-minute" class="time-nav-btn" title="-5 минут">-5м</button>
                    <button id="time-now" class="time-nav-btn time-now-btn" title="Ҳозирги вақт">Сейчас</button>
                    <button id="time-forward-minute" class="time-nav-btn" title="+5 минут">+5м</button>
                    <button id="time-forward-hour" class="time-nav-btn" title="+1 соат">+1ч</button>
                </div>

                <!-- Танланган вақт -->
                <div class="current-time-display">
                    <span id="current-selected-time">Реальное время</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Автоматик марказлаштириш кнопкаси -->
<div class="auto-center-control" title="Автоматик марказлаштириш">
    <div class="auto-center-toggle">
        <input type="checkbox" id="auto-center-toggle" checked>
        <span class="auto-center-slider"></span>
    </div>
</div>

<!-- Alarm mode toggle кнопкаси -->
<div class="alarm-mode-toggle">
    <button class="alarm-toggle-btn" title="Other alarms mode">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L1 21h22M12 6l7.53 13H4.47M11 10v4h2v-4m-2 6v2h2v-2" />
        </svg>
    </button>
</div>

<!-- Off Stations Panel toggle кнопкаси -->
<div class="off-stations-toggle">
    <button class="off-stations-toggle-btn" id="off-stations-toggle-btn" title="Места падения БС">
        <i class="fas fa-exclamation-circle"></i>
    </button>
</div>

<!-- РЦМУ No Traffic toggle кнопкаси -->
<div class="rcmu-no-traff-toggle">
    <button class="rcmu-no-traff-toggle-btn" id="rcmu-no-traff-toggle-btn" title="РЦМУ - БС без трафика">
        <i class="fas fa-satellite-dish"></i>
    </button>
</div>

<!-- Легенда компонент -->
{% include 'maps/components/downtime-legend.html' %}

<!-- Копирайт -->
<div class="custom-copyright">© 2025 Quality team</div>

<!-- Карта управление компонент -->
{% include 'maps/components/map-controls.html' %}

<!-- Передача данных из backend -->
{{ points_data_json|json_script:"points-data" }}
{{ user_privilege|json_script:"user-privilege" }}

<!-- Модульные JavaScript файлы -->
<!-- Translations модуллари -->
<script src="/maps/js/modules/core/translations-interface.js?v=1.0"></script>
<script src="/maps/js/modules/core/translations-regions.js?v=1.0"></script>
<script src="/maps/js/modules/core/translations-areas.js?v=1.8"></script>
<script src="/maps/js/modules/core/translations-utils.js?v=1.0"></script>
<script src="/maps/js/modules/core/constants.js?v=1.1"></script>
<script src="/maps/js/modules/core/utils.js?v=1.5"></script>
<!-- Time Machine модуллари -->
<script src="/maps/js/modules/ui/time-machine/controls.js?v=1.4"></script>
<script src="/maps/js/modules/ui/time-machine/slider.js?v=1.0"></script>
<script src="/maps/js/modules/ui/time-machine/navigation.js?v=1.0"></script>
<script src="/maps/js/modules/ui/time-machine/data.js?v=2.2"></script>
<script src="/maps/js/modules/ui/time-machine/utils.js?v=1.0"></script>
<script src="/maps/js/modules/ui/filters.js?v=2.1"></script>
<script src="/maps/js/modules/map/map-layers.js?v=1.1"></script>
<!-- Кластер модули -->
<script src="/maps/js/modules/map/cluster-core.js?v=1.0"></script>
<script src="/maps/js/modules/map/cluster-builders.js?v=1.0"></script>
<script src="/maps/js/modules/map/cluster-icons.js?v=1.0"></script>
<script src="/maps/js/modules/map/cluster-events.js?v=1.0"></script>
<script src="/maps/js/modules/map/markers.js?v=2.2"></script>
<script src="/maps/js/modules/ui/popup.js?v=2.1"></script>
<!-- Қидирув модуллари -->
<script src="/maps/js/modules/ui/search-api.js?v=1.0"></script>
<script src="/maps/js/modules/ui/search-marker.js?v=1.0"></script>
<script src="/maps/js/modules/ui/search-ui.js?v=1.0"></script>
<script src="/maps/js/modules/ui/search-autocomplete.js?v=1.0"></script>
<script src="/maps/js/modules/ui/search.js?v=1.5"></script>
<!-- Statistics модуллари -->
<script src="/maps/js/modules/data/statistics/regions.js?v=2.1"></script>
<script src="/maps/js/modules/data/statistics/areas.js?v=1.0"></script>
<script src="/maps/js/modules/data/statistics/overall.js?v=2.1"></script>
<script src="/maps/js/modules/data/statistics/duration.js?v=2.1"></script>
<script src="/maps/js/modules/data/data-loader.js?v=1.1"></script>
<!-- Alarms модули -->
<script src="/maps/js/modules/alarms/alarm-ui.js?v=1.0"></script>
<script src="/maps/js/modules/alarms/alarm-data.js?v=1.0"></script>
<script src="/maps/js/modules/alarms/alarm-markers.js?v=1.0"></script>
<script src="/maps/js/modules/alarms/alarm-stats.js?v=1.0"></script>
<script src="/maps/js/modules/alarms/alarm-manager.js?v=1.0"></script>
<!-- Off Stations Panel модуллари -->
<script src="/maps/js/modules/ui/off-stations/off-stations-config.js?v=1.1"></script>
<script src="/maps/js/modules/ui/off-stations/off-stations-utils.js?v=1.3"></script>
<script src="/maps/js/modules/ui/off-stations/off-stations-api.js?v=1.1"></script>
<script src="/maps/js/modules/ui/off-stations/off-stations-clustering.js?v=5.2"></script>
<script src="/maps/js/modules/ui/off-stations/off-stations-ui.js?v=1.3"></script>

<script src="/maps/js/modules/ui/off-stations/off-stations-reason-modal.js?v=1.1"></script>
<script src="/maps/js/modules/ui/off-stations/off-stations-reason-popup.js?v=1.2"></script>
<script src="/maps/js/modules/ui/off-stations/off-stations-manager.js?v=1.4"></script>
<!-- РЦМУ модули -->
<script src="/maps/js/modules/ui/rcmu-ui.js?v=1.0"></script>
<script src="/maps/js/modules/ui/rcmu-data.js?v=1.0"></script>
<script src="/maps/js/modules/ui/rcmu-display.js?v=1.0"></script>
<script src="/maps/js/modules/ui/rcmu-markers.js?v=1.0"></script>
<script src="/maps/js/main.js?v=1.4"></script>
</body>

{% endblock content %}