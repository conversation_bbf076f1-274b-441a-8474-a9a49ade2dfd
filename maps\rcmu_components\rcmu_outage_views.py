"""
RCMU авария сабабларини бошқариш модули
"""
import json
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from ..models import OutageReason
from .rcmu_permissions import check_rcmu_permission


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def save_outage_reason(request):
    """
    Сохранение причины для группы BS Down
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа.'}, status=403)
    
    try:
        data = json.loads(request.body)
        
        # Определение языка из запроса или сессии
        lang = data.get('lang', request.session.get('language', 'ru'))
        
        # Проверка обязательных полей
        required_fields = ['group_id', 'reason', 'stations_count', 'incident_timestamp']
        for field in required_fields:
            if field not in data or not data[field]:
                return JsonResponse({'error': f'Поле {field} обязательно.'}, status=400)
        
        # Проверка, существует ли уже причина для этой группы
        existing_reason = OutageReason.objects.filter(group_id=data['group_id']).first()
        
        if existing_reason:
            # Обновляем существующую причину
            existing_reason.set_reason(data['reason'], lang)
            existing_reason.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Причина успешно обновлена.',
                'reason_id': existing_reason.id
            })
        else:
            # Создаем новую причину
            new_reason = OutageReason(
                group_id=data['group_id'],
                area_name=data.get('area_name', ''),
                region_name=data.get('region_name', ''),
                stations_count=data['stations_count'],
                stations_data=json.dumps(data.get('stations_data', [])),
                incident_timestamp=datetime.fromisoformat(data['incident_timestamp'].replace('Z', '').replace('+00:00', '')),
                created_by=request.user,
                created_by=request.user
            )
            # Устанавливаем причину на нужном языке
            new_reason.set_reason(data['reason'], lang)
            new_reason.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Причина успешно сохранена.',
                'reason_id': new_reason.id
            })
            
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при сохранении: {str(e)}'}, status=500)


@login_required
def get_outage_reasons(request):
    """
    Получение всех причин для BS Down зон
    """
    reasons = OutageReason.objects.all()
    
    data = []
    for reason in reasons:
        data.append({
            'id': reason.id,
            'group_id': reason.group_id,
            'reason': reason.reason,
            'reason_ru': reason.reason_ru,
            'reason_en': reason.reason_en,
            'area_name': reason.area_name,
            'region_name': reason.region_name,
            'stations_count': reason.stations_count,
            'incident_timestamp': reason.incident_timestamp.isoformat(),
            'created_at': reason.created_at.isoformat(),
            'created_by': reason.created_by.username if reason.created_by else None
        })
    
    return JsonResponse({'reasons': data})


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def update_outage_reason(request, pk):
    """
    Обновление причины
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа.'}, status=403)
    
    reason = get_object_or_404(OutageReason, pk=pk)
    
    try:
        data = json.loads(request.body)
        lang = data.get('lang', request.session.get('language', 'ru'))
        
        # Обновляем причину на нужном языке
        if 'reason' in data:
            reason.set_reason(data['reason'], lang)
            reason.save()
            
            return JsonResponse({
                'success': True,
                'message': 'Причина успешно обновлена.'
            })
        else:
            return JsonResponse({'error': 'Причина не указана.'}, status=400)
            
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при обновлении: {str(e)}'}, status=500)


@login_required
@require_http_methods(["DELETE"])
def delete_outage_reason(request, pk):
    """
    Удаление причины
    """
    # Проверка прав доступа (только администраторы могут удалять)
    if not (hasattr(request.user, 'privilege_id') and request.user.privilege_id == 1):
        return JsonResponse({'error': 'У вас нет прав для удаления.'}, status=403)
    
    reason = get_object_or_404(OutageReason, pk=pk)
    
    try:
        reason.delete()
        return JsonResponse({
            'success': True,
            'message': 'Причина успешно удалена.'
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при удалении: {str(e)}'}, status=500) 