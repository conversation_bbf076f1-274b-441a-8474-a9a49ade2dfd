/**
 * 🗺️ Асосий карта модули координатори
 * Бу файл барча модулларни импорт қилади ва уларнинг инициализацияси учун масъул
 * 
 * ✅ Модулларга бўлинган структура - оригинал 752 сатр дан ~30 сатргача қисқарди
 * 
 * ЭСЛАТМА: Кўпгина модуллар HTML шаблонида аллақачон юкланган.
 * Бу файл фақат қўшимча модулларни юклайди ва координация қилади.
 */

// ===============================
// 📦 ҚЎШИМЧА МОДУЛЛАР ИМПОРТИ (HTML да йўқ бўлганлар)
// ===============================

// 🔧 Базавий модуллар (HTML да йўқлари)
document.write('<script src="/maps/js/modules/core/utility-functions.js"></script>');
document.write('<script src="/maps/js/modules/core/app-core.js"></script>');

// 🗺️ Карта модуллари (HTML да йўқлари)
document.write('<script src="/maps/js/modules/map/map-initialization.js"></script>');
document.write('<script src="/maps/js/modules/map/yandex-map.js"></script>');
document.write('<script src="/maps/js/modules/map/map-layer-handlers.js"></script>');

// 📊 Маълумотлар модуллари (HTML да йўқлари)
document.write('<script src="/maps/js/modules/data/region-area-handlers.js"></script>');

// 🎨 UI модуллари (HTML да йўқлари)
document.write('<script src="/maps/js/modules/ui/ui-initialization.js"></script>');

// ЭСЛАТМА: Қуйидаги модуллар HTML шаблонида аллақачон юкланган:
// - translations-interface.js, translations-regions.js, translations-areas.js, translations-utils.js, constants.js, utils.js ✅
// - time-machine/*.js, filters.js, search.js ✅  
// - map-layers.js, cluster-core.js, cluster-builders.js, cluster-icons.js, cluster-events.js, markers.js ✅
// - popup.js, statistics/*.js, data-loader.js ✅
// - alarm-manager.js, off-stations/*.js ✅
// - rcmu-markers.js ✅

// ===============================
// 🚀 БАРЧА МОДУЛЛАР ЮКЛАНГАНИДАН КЕЙИН ИНИЦИАЛИЗАЦИЯ
// ===============================

// Модуль дублировкаларини олдини олиш тизими
(function () {
    'use strict';

    // Дублировка текшириш
    if (window.mapAppInitialized) {
        return; // Агар аллақачон инициализация қилинган бўлса, чиқиб кетамиз
    }

    window.mapAppInitialized = true;

    // Карта иловаси инициализацияси
    function initializeMapApp() {
        try {
            // Off Stations Panel инициализацияси
            if (window.initOffStationsPanel) {
                window.initOffStationsPanel();
            }

            // РЦМУ маркерлар инициализацияси  
            if (window.initRcmuMarkers) {
                window.initRcmuMarkers();
            }

            // Автоматик маълумот юклаш
            if (window.refreshMapData) {
                window.refreshMapData();
            }

        } catch (error) {
            console.error('Карта иловаси инициализация хатоси:', error);
        }
    }

    // DOM тайёр бўлганда инициализация қилиш
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMapApp);
    } else {
        initializeMapApp();
    }

})();

// main.js модули юкланди 