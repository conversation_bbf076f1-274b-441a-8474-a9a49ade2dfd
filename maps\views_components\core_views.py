"""
Асосий карта кўриниш функциялари
"""
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from ..map_data_utils import get_bs_data_with_alarms, prepare_map_data_response


@login_required()
def map_view(request):
    """Модуляр карта кўриниши"""
    # Умумий функция орқали БС ва авария маълумотларини олиш
    bs_stations, regions = get_bs_data_with_alarms()

    # Шаблонга узатиш учун маълумотларни тайёрлаш
    map_data = prepare_map_data_response(bs_stations, regions)
    
    # Фойдаланувчи роли маълумотларини тайёрлаш
    user_privilege = {
        'id': getattr(request.user, 'privilege_id', None),
        'name': getattr(request.user.privilege, 'privilege', '') if hasattr(request.user, 'privilege') and request.user.privilege else ''
    }
    
    # Модуляр шаблонни ишлатиш
    return render(request, 'maps/map.html', {
        'points_data_json': map_data,
        'user_privilege': user_privilege
    }) 