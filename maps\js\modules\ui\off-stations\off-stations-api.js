// Off Stations API модули
(function() {
    'use strict';
    
    // Конфигурация ва утилиталарни олиш
    const { CONFIG, ALLOWED_ROLES } = window.OffStationsConfig;
    const { getCsrfToken } = window.OffStationsUtils;

    // Мавжуд причиналарни юклаш
    function loadExistingReasons() {
        return fetch('/map/rcmu/api/get-reasons/')
            .then(response => response.json())
            .then(data => {
                if (data.reasons) {
                    // Причиналарни localStorage га сақлаш
                    data.reasons.forEach(reason => {
                        const groupId = reason.group_id;
                        
                        // Иккала тилдаги причиналарни сақлаш
                        if (reason.reason_ru) {
                            localStorage.setItem(`reason_${groupId}_ru`, reason.reason_ru);
                        }
                        if (reason.reason_en) {
                            localStorage.setItem(`reason_${groupId}_en`, reason.reason_en);
                        }
                        
                        // Умумий fallback
                        if (reason.reason) {
                            localStorage.setItem(`reason_${groupId}`, reason.reason);
                        }
                    });
                    
                    return data.reasons;
                }
                return [];
            })
            .catch(error => {
                console.error('Error loading existing reasons:', error);
                return [];
            });
    }

    // Причинани серверга сақлаш
    function saveReasonToServer(groupId, reason, groupData) {
        // Текущий язык
        const currentLang = window.currentLanguage || 'ru';
        
        const data = {
            group_id: groupId,
            reason: reason,
            lang: currentLang,
            area_name: groupData.area.area || '',
            region_name: groupData.area.region || '',
            stations_count: groupData.stations.length,
            stations_data: groupData.stations.map(s => ({
                id: s.id,
                name: s.bsName || s.name,
                lat: s.lat,
                lon: s.lon,
                calc_time: s.calcTime || 0
            })),
            incident_timestamp: new Date().toISOString().replace('Z', '').replace('.000', '')
        };

        return fetch('/map/rcmu/api/save-reason/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify(data)
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    console.log('Reason saved successfully');
                    return true;
                } else {
                    console.error('Failed to save reason:', result.error);
                    alert('Ошибка: ' + result.error);
                    return false;
                }
            })
            .catch(error => {
                console.error('Error saving reason:', error);
                alert('Произошла ошибка при сохранении причины');
                return false;
            });
    }

    // РЦМУ рухсатини текшириш
    function checkRcmuPermission() {
        // Глобал ўзгарувчидан фойдаланувчи маълумотларини олиш
        if (window.userPrivilege) {
            const privilegeId = window.userPrivilege.id;
            const privilegeName = window.userPrivilege.name;

            return ALLOWED_ROLES.IDS.includes(privilegeId) ||
                ALLOWED_ROLES.NAMES.some(name => name.toUpperCase() === privilegeName.toUpperCase());
        }

        // Агар маълумот йўқ бўлса, рухсат бермаслик (хавфсизлик учун)
        return false;
    }

    // API орқали РЦМУ рухсатини текшириш
    function checkRcmuPermissionViaAPI() {
        // Синхрон текшириш учун localStorage дан олдинги натижани олиш
        const cachedPermission = localStorage.getItem(CONFIG.STORAGE_KEYS.RCMU_PERMISSION);
        if (cachedPermission !== null) {
            return cachedPermission === 'true';
        }

        // Асинхрон текшириш ва кешлаш
        fetch('/map/api/check-rcmu-permission/')
            .then(response => response.json())
            .then(data => {
                localStorage.setItem(CONFIG.STORAGE_KEYS.RCMU_PERMISSION, data.hasPermission ? 'true' : 'false');
            })
            .catch(error => {
                console.error('Error checking RCMU permission:', error);
                localStorage.setItem(CONFIG.STORAGE_KEYS.RCMU_PERMISSION, 'false');
            });

        // Дефолт қийматни қайтариш
        return false;
    }
    
    // Глобал объектга экспорт қилиш
    window.OffStationsAPI = {
        loadExistingReasons: loadExistingReasons,
        saveReasonToServer: saveReasonToServer,
        checkRcmuPermission: checkRcmuPermission,
        checkRcmuPermissionViaAPI: checkRcmuPermissionViaAPI
    };
})(); 