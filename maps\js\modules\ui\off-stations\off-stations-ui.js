// Off Stations UI компонентлари модули
(function() {
    'use strict';
    
    // Конфигурация ва утилиталарни олиш
    const { CONFIG } = window.OffStationsConfig;
    const { getTranslations } = window.OffStationsUtils;

    // Панел HTML яратиш
    function createPanelHTML() {
        const panel = document.createElement('div');
        panel.className = CONFIG.CSS_CLASSES.PANEL;
        panel.id = CONFIG.DOM_IDS.PANEL;

        const translations = getTranslations();

        panel.innerHTML = `
            <div class="panel-header">
                <h3>
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="${CONFIG.DOM_IDS.PANEL_TITLE}">${translations.title}</span>
                </h3>
                <span class="station-count" id="${CONFIG.DOM_IDS.TOTAL_COUNT}">0</span>
            </div>
            <div class="panel-content" id="${CONFIG.DOM_IDS.PANEL_CONTENT}">
                <div class="auto-update-indicator" id="${CONFIG.DOM_IDS.UPDATE_INDICATOR}"></div>
                <div id="${CONFIG.DOM_IDS.GROUPS_CONTAINER}">
                    <div class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <p id="${CONFIG.DOM_IDS.EMPTY_MESSAGE}">${translations.emptyMessage}</p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(panel);
    }

    // Группа HTML яратиш
    function createGroupHTML(group, index, isNew) {
        const translations = getTranslations();
        
        // Ҳар бир БС нинг downtime ни ҳисоблаш
        let maxDowntime = 0;
        group.stations.forEach(station => {
            if (station.calcTime) {
                const hours = Math.floor(station.calcTime / 3600);
                if (hours > maxDowntime) {
                    maxDowntime = hours;
                }
            }
        });

        // Причина маълумотини олиш
        const groupId = window.OffStationsUtils.generateGroupId(group);
        const currentLang = window.currentLanguage || 'ru';
        const savedReason = localStorage.getItem(`reason_${groupId}_${currentLang}`) || 
                           localStorage.getItem(`reason_${groupId}`);
        
        let reasonAttr = '';
        let reasonIndicator = '';
        
        if (savedReason) {
            reasonAttr = `data-reason="${savedReason}"`;
            reasonIndicator = `<i class="fas fa-comment-alt reason-indicator" title="${translations.reason_specified}"></i>`;
        }

        // Район маълумотларини форматлаш
        let locationInfo = group.area.area;
        if (group.area.areaCount > 1) {
            const lang = window.currentLanguage || 'ru';
            const districtText = lang === 'en' ? 'districts' : 'район';
            locationInfo += ` (${group.area.areaCount} ${districtText})`;
        }

        return `
            <div class="station-group ${isNew ? CONFIG.CSS_CLASSES.NEW_ITEM : ''}" id="station-group-${index}" ${reasonAttr}>
                <div class="station-group-header">
                    <div class="location-name">
                        <i class="fas fa-map-marker-alt"></i> ${locationInfo}
                        ${reasonIndicator}
                    </div>
                    <span class="station-count-badge">${group.stations.length}</span>
                </div>
                <div class="station-info">
                    <div>${group.area.region}</div>
                    ${maxDowntime > 0 ? `<div class="downtime-info">${translations.downtime}: ${maxDowntime}+ ${translations.hours}</div>` : ''}
                    ${group.area.areaCount > 1 ? `<div class="area-info">${group.area.areaCount} ${translations.districts}: ${group.area.area}</div>` : ''}
                </div>
                <div class="station-list">
                    ${group.stations.slice(0, 3).map(station => `
                        <div class="station-name">
                            <i class="fas fa-circle"></i> ${station.bsName || station.name}
                        </div>
                    `).join('')}
                    ${group.stations.length > 3 ? `<div class="station-name">... ${translations.and} ${group.stations.length - 3}</div>` : ''}
                </div>
            </div>
        `;
    }



    // Группа попап контенти
    function createGroupPopupContent(group) {
        const translations = getTranslations();
        
        // Группа учун ID генерация қилиш
        const groupId = window.OffStationsUtils.generateGroupId(group);
        
        // Сақланган причинани олиш
        const currentLang = window.currentLanguage || 'ru';
        const savedReason = localStorage.getItem(`reason_${groupId}_${currentLang}`) || 
                           localStorage.getItem(`reason_${groupId}`);
        
        // Район маълумотларини форматлаш
        let locationInfo = group.area.area;
        if (group.area.areaCount > 1) {
            const lang = window.currentLanguage || 'ru';
            const districtText = lang === 'en' ? 'districts' : 'район';
            locationInfo += ` (${group.area.areaCount} ${districtText})`;
        }

        return `
            <div style="min-width: 300px; max-width: 400px;">
                <div style="background: #dc3545; color: white; padding: 10px 15px; margin: -12px -12px 10px -12px; border-radius: 4px 4px 0 0;">
                    <h4 style="margin: 0; font-size: 16px;">
                        <i class="fas fa-exclamation-triangle"></i> ${translations.bsDownZone}
                    </h4>
                </div>
                
                <div style="padding: 0 5px;">
                    <p style="margin: 5px 0; font-size: 14px;">
                        <strong><i class="fas fa-map-marker-alt"></i> ${locationInfo}</strong>
                    </p>
                    <p style="margin: 5px 0; color: #666; font-size: 13px;">${group.area.region}</p>
                    
                    ${savedReason ? `
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <p style="margin: 0 0 5px 0; font-weight: bold; color: #333; font-size: 13px;">
                                <i class="fas fa-info-circle"></i> ${translations.reason || 'Причина'}:
                            </p>
                            <p style="margin: 0; color: #555; font-size: 13px;">${savedReason}</p>
                        </div>
                    ` : `
                        <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #ffeeba;">
                            <p style="margin: 0; color: #856404; font-size: 13px;">
                                <i class="fas fa-exclamation-triangle"></i> ${translations.noReason || 'Причина не указана'}
                            </p>
                        </div>
                    `}
                    
                    <!-- Причина киритиш формаси -->
                    <div id="reason-form-${groupId}" style="display: none; margin: 10px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6;">
                            ${window.userPrivilege && (window.userPrivilege.id === 1 || window.userPrivilege.id === 10) ? `
                                <!-- Админлар учун иккала тил -->
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 13px;">
                                        ${translations.reason || 'Причина'} (рус):
                                    </label>
                                    <textarea id="reason-input-ru-${groupId}" 
                                            style="width: 100%; min-height: 60px; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 13px; resize: vertical;"
                                            placeholder="Введите причину на русском...">${localStorage.getItem(`reason_${groupId}_ru`) || savedReason || ''}</textarea>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 13px;">
                                        ${translations.reason || 'Reason'} (eng):
                                    </label>
                                    <textarea id="reason-input-en-${groupId}" 
                                            style="width: 100%; min-height: 60px; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 13px; resize: vertical;"
                                            placeholder="Enter reason in English...">${localStorage.getItem(`reason_${groupId}_en`) || ''}</textarea>
                                </div>
                            ` : `
                                <!-- Оддий фойдаланувчилар учун битта тил -->
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 13px;">
                                        ${translations.reason || 'Причина'}:
                                    </label>
                                    <textarea id="reason-input-${groupId}" 
                                            style="width: 100%; min-height: 80px; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 13px; resize: vertical;"
                                            placeholder="${translations.enterReason || 'Введите причину...'}">${savedReason || ''}</textarea>
                                </div>
                            `}
                            <div style="display: flex; gap: 10px;">
                                <button onclick="saveReasonFromPopup('${groupId}')" 
                                        style="flex: 1; padding: 8px 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                                    <i class="fas fa-save"></i> ${translations.save || 'Сохранить'}
                                </button>
                                <button onclick="cancelReasonForm('${groupId}')" 
                                        style="flex: 1; padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                                    <i class="fas fa-times"></i> ${translations.cancel || 'Отмена'}
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <p style="margin: 5px 0; font-weight: bold; font-size: 13px;">
                            <i class="fas fa-broadcast-tower"></i> ${group.stations.length} ${translations.bs_count}:
                        </p>
                        <div style="max-height: 150px; overflow-y: auto; background: #f8f9fa; padding: 8px; border-radius: 4px;">
                            ${group.stations.map((s, index) => `
                                <div style="padding: 3px 0; font-size: 12px; ${index > 0 ? 'border-top: 1px solid #dee2e6;' : ''}">
                                    ${s.bsName || s.name}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 10px; margin-top: 15px;">
                        <button id="toggle-reason-btn-${groupId}" 
                                onclick="toggleReasonForm('${groupId}')" 
                                style="flex: 1; padding: 8px 12px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                            <i class="fas fa-edit"></i> ${savedReason ? translations.edit_reason || 'Изменить причину' : translations.add_reason || 'Добавить причину'}
                        </button>
                        <button onclick="window.location.href='/map/rcmu/treez/'" 
                                style="flex: 1; padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px;">
                            <i class="fas fa-list"></i> ${translations.manage_reasons || 'Все причины'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Empty state HTML
    function createEmptyStateHTML() {
        const translations = getTranslations();
        return `
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <p id="${CONFIG.DOM_IDS.EMPTY_MESSAGE}">${translations.emptyMessage}</p>
            </div>
        `;
    }

    // Панел UI ни янгилаш
    function updatePanelContent(container, html) {
        if (container) {
            container.innerHTML = html;
        }
    }

    // Тил ўзгарганда элементларни янгилаш
    function updateLanguageElements() {
        const translations = getTranslations();
        
        const title = document.getElementById(CONFIG.DOM_IDS.PANEL_TITLE);
        const emptyMessage = document.getElementById(CONFIG.DOM_IDS.EMPTY_MESSAGE);
        const toggleBtn = document.querySelector('.off-stations-toggle-btn');

        if (title) {
            title.textContent = translations.title;
        }

        if (emptyMessage) {
            emptyMessage.textContent = translations.emptyMessage;
        }

        // Кнопка tooltip ни янгилаш
        if (toggleBtn) {
            toggleBtn.title = translations.title;
        }
    }
    
    // Глобал объектга экспорт қилиш
    window.OffStationsUI = {
        createPanelHTML: createPanelHTML,
        createGroupHTML: createGroupHTML,
        createGroupPopupContent: createGroupPopupContent,
        createEmptyStateHTML: createEmptyStateHTML,
        updatePanelContent: updatePanelContent,
        updateLanguageElements: updateLanguageElements
    };
})(); 