from django.contrib import admin
from .models import RcmuData


@admin.register(RcmuData)
class RcmuDataAdmin(admin.ModelAdmin):
    list_display = [
        'site', 'bs_name', 'region', 'branch', 'date_lego', 
        'responsible', 'has_coordinates', 'has_quality_comment', 'created_at'
    ]
    list_filter = ['region', 'branch', 'date_lego', 'responsible', 'created_at']
    search_fields = ['site', 'bs_name', 'region', 'branch', 'responsible']
    readonly_fields = ['created_at', 'updated_at', 'created_by']
    
    fieldsets = (
        ('Основная информация', {
            'fields': ('site', 'bs_name', 'region', 'branch', 'freq')
        }),
        ('Даты', {
            'fields': ('date_lego', 'date_performance', 'estimated_air_date', 'total_down_time_days')
        }),
        ('Ответственность и разрешения', {
            'fields': ('responsible', 'operating_permit')
        }),
        ('Причины и комментарии', {
            'fields': ('cause_ru', 'cause_eng', 'comments', 'quality_comment')
        }),
        ('Координаты', {
            'fields': ('lat', 'lon'),
            'description': 'Координаты автоматически заполняются из таблицы bsview_bsbeeline'
        }),
        ('Служебная информация', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )
    
    def has_coordinates(self, obj):
        return bool(obj.lat and obj.lon)
    has_coordinates.boolean = True
    has_coordinates.short_description = 'Есть координаты'
    
    def has_quality_comment(self, obj):
        return bool(obj.quality_comment)
    has_quality_comment.boolean = True
    has_quality_comment.short_description = 'Есть комментарий качественника'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Если создается новая запись
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Можно добавить фильтрацию по правам пользователя если нужно
        return qs
