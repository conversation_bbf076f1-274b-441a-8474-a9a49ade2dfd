// РЦМУ Display модули - маркерларни кўрсатиш функциялари

(function() {
    'use strict';

    // Глобал ўзгарувчилар
    let rcmuMarkers = [];
    let rcmuLayerGroup = null;

    // Конфигурация
    const CONFIG = {
        MARKER_COLOR: '#FF8C00', // DarkOrange ранг
        MARKER_SIZE: 10,
    };

    // РЦМУ Display системасини инициализация қилиш
    window.initRcmuDisplay = function() {
        // Карта тайёр бўлганда маркерлар группасини яратиш
        if (window.mymap) {
            rcmuLayerGroup = L.layerGroup().addTo(window.mymap);

            // Зум ўзгарганда маркер размерини янгилаш
            window.mymap.on('zoomend', function() {
                updateRcmuMarkersSize();
            });
        }

        // Асосий маркерлар янгиланганда РЦМУ маркерларини қайта қўшиш
        setupRcmuPersistence();
    };

    // РЦМУ маркерларини автоматик янгиланишдан сақлаш
    function setupRcmuPersistence() {
        // Асосий updateMapMarkers функциясини ўраб олиш
        if (window.updateMapMarkers && !window.updateMapMarkers._rcmuWrapped) {
            const originalUpdateMapMarkers = window.updateMapMarkers;

            window.updateMapMarkers = function(stations, isOtherAlarmsMode = false) {
                // РЦМУ маркерлари кўринадими?
                const rcmuVisible = checkRcmuVisible();

                // Асосий функцияни чақириш
                originalUpdateMapMarkers.call(this, stations, isOtherAlarmsMode);

                // Агар РЦМУ маркерлари кўринган бўлса, қайта қўшиш
                if (rcmuVisible && window.getRcmuData) {
                    const rcmuData = window.getRcmuData();
                    if (rcmuData.length > 0) {
                        setTimeout(() => {
                            // Фильтрга мувофиқ РЦМУ маркерларини қайта кўрсатиш
                            showRcmuMarkers(rcmuData);
                        }, 100); // Кичик кечикиш
                    }
                }
            };

            // Белгилаш - функция ўралганлигини
            window.updateMapMarkers._rcmuWrapped = true;
        }
    }

    // РЦМУ маркерларини кўрсатиш
    window.showRcmuMarkers = function(rcmuData) {
        if (!window.mymap) {
            console.log('РЦМУ: Карта мавжуд эмас');
            return;
        }

        // === Аввал очиқ popup ни эслаб қолиш ===
        let openedRcmuSite = null;
        if (window.mymap && window.mymap._popup && window.mymap._popup._source) {
            const src = window.mymap._popup._source;
            if (src.options && src.options.pointData && src.options.pointData.isRcmu) {
                openedRcmuSite = src.options.pointData.site || src.options.pointData.bsName;
            }
        }

        // Олдинги маркерларни тозалаш
        hideRcmuMarkers();

        let addedCount = 0;
        const newRcmuMarkers = [];

        // Ҳозирги фильтр ҳолатини олиш
        const filteredBsNumbers = getFilteredBsNumbers();
        const shouldApplyFilter = filteredBsNumbers !== null;

        rcmuData.forEach((item, index) => {
            if (!item.lat || !item.lon) {
                return;
            }

            // Фильтр текшириш
            if (shouldApplyFilter && !filteredBsNumbers.has(item.site)) {
                return;
            }

            // Координаталарни тозалаш ва конвертация қилиш
            let lat = item.lat.toString().replace(',', '.');
            let lon = item.lon.toString().replace(',', '.');

            lat = parseFloat(lat);
            lon = parseFloat(lon);

            if (isNaN(lat) || isNaN(lon)) {
                console.log(`РЦМУ: ${item.site} учун нотўғри координаталар: ${item.lat}, ${item.lon}`);
                return;
            }

            // Маркер яратиш
            const marker = L.circleMarker([lat, lon], {
                radius: getCircleRadius(window.mymap.getZoom()),
                fillColor: CONFIG.MARKER_COLOR,
                color: '#000000',
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8,
                className: 'rcmu-marker',
                pointData: {
                    ...item,
                    type: 'rcmu',
                    bsName: item.bs_name || item.site,
                    name: item.bs_name || item.site,
                    region_id: item.region_id || null,
                    area_id: item.area_id || null,
                    isRcmu: true
                }
            });

            // Попап контенти
            const popupContent = createRcmuPopupContent(item);
            marker.bindPopup(popupContent, {
                maxWidth: 300,
                className: 'rcmu-popup'
            });

            // Hover эффекти
            marker.on('mouseover', function(e) {
                this.setStyle({
                    weight: 3,
                    color: '#ffffff'
                });
            });

            marker.on('mouseout', function(e) {
                this.setStyle({
                    weight: 1,
                    color: '#000000'
                });
            });

            newRcmuMarkers.push(marker);
            addedCount++;
        });

        // Маркерларни кластер системасига қўшиш
        if (newRcmuMarkers.length > 0) {
            rcmuMarkers = newRcmuMarkers;

            // РЦМУ маркерларини pointLayers массивига қўшиш
            if (!window.pointLayers) {
                window.pointLayers = [];
            }

            // РЦМУ маркерларини pointLayers га қўшиш
            window.pointLayers.push(...rcmuMarkers);

            // Кластерларни қайта қуриш
            if (typeof window.rebuildClusters === 'function') {
                window.rebuildClusters();
            } else {
                // Агар кластер системаси мавжуд бўлмаса, тўғридан-тўғри қўшиш
                rcmuMarkers.forEach(marker => {
                    marker.addTo(window.mymap);
                });
            }

            // === Аввалги popup ни қайта очиш ===
            if (openedRcmuSite) {
                const targetMarker = rcmuMarkers.find(m => 
                    m.options && m.options.pointData && 
                    (m.options.pointData.site === openedRcmuSite || m.options.pointData.bsName === openedRcmuSite)
                );
                if (targetMarker) {
                    targetMarker.openPopup();
                }
            }
        }

        // Статистикани янгилаш
        updateRcmuStats(addedCount);
    };

    // РЦМУ маркерларини яшириш
    window.hideRcmuMarkers = function() {
        if (rcmuMarkers.length === 0) return;

        // РЦМУ маркерларини pointLayers дан олиб ташлаш
        if (window.pointLayers && window.pointLayers.length > 0) {
            window.pointLayers = window.pointLayers.filter(marker => {
                const isRcmuMarker = marker.options && marker.options.pointData && marker.options.pointData.isRcmu;
                if (isRcmuMarker) {
                    if (window.mymap.hasLayer(marker)) {
                        window.mymap.removeLayer(marker);
                    }
                    return false;
                }
                return true;
            });

            // Кластерларни қайта қуриш
            if (typeof window.rebuildClusters === 'function') {
                window.rebuildClusters();
            }
        } else {
            // Тўғридан-тўғри олиб ташлаш
            rcmuMarkers.forEach(marker => {
                if (window.mymap.hasLayer(marker)) {
                    window.mymap.removeLayer(marker);
                }
            });
        }

        // Layer group ни ҳам тозалаш
        if (rcmuLayerGroup) {
            rcmuLayerGroup.clearLayers();
        }

        rcmuMarkers = [];
        updateRcmuStats(0);
    };

    // Фильтрланган БС рақамларини олиш
    function getFilteredBsNumbers() {
        const currentRegionId = document.getElementById('region').value;
        const currentStatus = document.getElementById('status').value;
        const currentAreaId = document.getElementById('area').value;

        // Фильтр йўқ бўлса
        if (!currentRegionId && (!currentStatus || currentStatus === 'all')) {
            return null;
        }

        // Фильтрланган БС рақамларини аниқлаш
        const filteredStations = window.globalStations.filter(station => {
            const matchesRegion = !currentRegionId || station.region_id == currentRegionId;
            const matchesArea = !currentAreaId || station.area_id == currentAreaId;
            
            let matchesStatus = true;
            if (currentStatus && currentStatus !== 'all') {
                if (currentStatus === 'offline') {
                    matchesStatus = station.status === true || station.status === 'true' || 
                                  station.status === 1 || station.status === '1';
                } else if (currentStatus === 'online') {
                    matchesStatus = station.status === false || station.status === 'false' || 
                                  station.status === 0 || station.status === '0';
                }
            }
            
            return matchesRegion && matchesArea && matchesStatus;
        });

        // БС рақамларини олиш
        const bsNumbers = filteredStations.map(station => {
            return station.bsNumber || station.bsnumber || station.number || station.bsnum;
        }).filter(num => num);

        return new Set(bsNumbers);
    }

    // Зум бўйича маркер размерини олиш
    function getCircleRadius(zoom) {
        if (zoom < 6) return 3;
        else if (zoom < 10) return 6;
        else return 12;
    }

    // Зум ўзгарганда РЦМУ маркерлар размерини янгилаш
    function updateRcmuMarkersSize() {
        if (rcmuMarkers.length === 0) return;

        const currentZoom = window.mymap.getZoom();
        const newRadius = getCircleRadius(currentZoom);

        if (window.pointLayers && window.pointLayers.length > 0) {
            window.pointLayers.forEach(marker => {
                if (marker.options && marker.options.pointData && marker.options.pointData.isRcmu) {
                    if (marker.setRadius) {
                        marker.setRadius(newRadius);
                    }
                }
            });
        } else {
            rcmuMarkers.forEach(marker => {
                if (marker.setRadius) {
                    marker.setRadius(newRadius);
                }
            });
        }
    }

    // РЦМУ кўринадими текшириш
    function checkRcmuVisible() {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        return toggleBtn && toggleBtn.classList.contains('active');
    }

    // РЦМУ popup контенти яратиш
    function createRcmuPopupContent(item) {
        const lang = window.currentLanguage || 'ru';
        
        const formatDate = (dateStr) => {
            if (!dateStr) return '—';
            const date = new Date(dateStr);
            return date.toLocaleDateString('ru-RU');
        };

        const formatCoords = (lat, lon) => {
            if (!lat || !lon) return '—';
            return `${parseFloat(lat).toFixed(6)}, ${parseFloat(lon).toFixed(6)}`;
        };

        return `
            <div class="rcmu-popup-content">
                <h6 class="popup-title">
                    <i class="fas fa-satellite-dish text-warning me-2"></i>
                    ${lang === 'ru' ? 'РЦМУ - БС без трафика' : 'RCMU - BS without traffic'}
                </h6>
                
                <div class="popup-info">
                    <div class="info-row">
                        <strong>Site:</strong> ${item.site || '—'}
                    </div>
                    <div class="info-row">
                        <strong>${lang === 'ru' ? 'Название БС:' : 'BS Name'}:</strong> ${item.bs_name || '—'}
                    </div>
                    <div class="info-row">
                        <strong>${lang === 'ru' ? 'Регион:' : 'Region'}:</strong> ${item.region || '—'}
                    </div>
                    <div class="info-row">
                        <strong>${lang === 'ru' ? 'Филиал:' : 'Branch'}:</strong> ${item.branch || '—'}
                    </div>
                    <div class="info-row">
                        <strong>${lang === 'ru' ? 'Дата (LEGO):' : 'Date (LEGO)'}:</strong> ${formatDate(item.date_lego)}
                    </div>
                    <div class="info-row">
                        <strong>${lang === 'ru' ? 'Ответственный:' : 'Responsible'}:</strong> ${item.responsible || '—'}
                    </div>
                    <div class="info-row">
                        <strong>${lang === 'ru' ? 'Координаты:' : 'Coordinates'}:</strong> ${formatCoords(item.lat, item.lon)}
                    </div>
                    ${item.quality_comment ? `
                        <div class="info-row">
                            <strong>${lang === 'ru' ? 'Комментарий:' : 'Comment'}:</strong> 
                            <div class="comment-text">${item.quality_comment}</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // РЦМУ статистикасини янгилаш
    function updateRcmuStats(count) {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        if (toggleBtn) {
            const lang = window.currentLanguage || 'ru';
            const baseTitle = lang === 'en' ? 'RCMU - BS without traffic' : 'РЦМУ - БС без трафика';
            toggleBtn.title = count > 0 ? `${baseTitle} (${count})` : baseTitle;
        }
    }

})(); 