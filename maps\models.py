from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class RcmuData(models.Model):
    """
    Модель для хранения данных РЦМУ из Excel файлов
    """
    # Основные поля из Excel
    branch = models.CharField(max_length=100, verbose_name="Филиал", null=True, blank=True)
    region = models.CharField(max_length=100, verbose_name="Регион", null=True, blank=True)
    region_id = models.IntegerField(verbose_name="ID региона", null=True, blank=True)
    date_lego = models.DateField(verbose_name="Дата (LEGO)", null=True, blank=True)
    date_performance = models.DateField(verbose_name="Дата (performance)", null=True, blank=True)
    site = models.CharField(max_length=20, verbose_name="Site", db_index=True)  # Номер БС для связи с bsview_bsbeeline
    freq = models.CharField(max_length=50, verbose_name="Частота", null=True, blank=True)
    bs_name = models.CharField(max_length=100, verbose_name="Название БС", null=True, blank=True)
    operating_permit = models.CharField(max_length=100, verbose_name="Разрешение на работу", null=True, blank=True)
    comments = models.TextField(verbose_name="Комментарии", null=True, blank=True)
    cause_ru = models.TextField(verbose_name="Причина (рус)", null=True, blank=True)
    cause_eng = models.TextField(verbose_name="Причина (англ)", null=True, blank=True)
    responsible = models.CharField(max_length=100, verbose_name="Ответственный", null=True, blank=True)
    estimated_air_date = models.CharField(max_length=100, verbose_name="Предполагаемая дата запуска", null=True, blank=True)
    total_down_time_days = models.CharField(max_length=50, verbose_name="Общее время простоя (дни)", null=True, blank=True)
    
    # Дополнительное поле для комментария качественника
    quality_comment = models.TextField(verbose_name="Комментарий качественника", null=True, blank=True)
    
    # Координаты (будут заполняться из bsview_bsbeeline)
    lat = models.CharField(max_length=20, verbose_name="Широта", null=True, blank=True)
    lon = models.CharField(max_length=20, verbose_name="Долгота", null=True, blank=True)
    
    # Служебные поля
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Дата создания")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Дата обновления")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="Создано пользователем")
    
    class Meta:
        db_table = 'rcmu_data'
        verbose_name = "Данные РЦМУ"
        verbose_name_plural = "Данные РЦМУ"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['site']),
            models.Index(fields=['date_lego']),
            models.Index(fields=['region']),
        ]
    
    def __str__(self):
        return f"РЦМУ {self.site} - {self.bs_name or 'Без названия'}"
    
    def get_coordinates_display(self):
        """Возвращает координаты в удобном формате"""
        if self.lat and self.lon:
            return f"{self.lat}, {self.lon}"
        return "Координаты не найдены"


class OutageReason(models.Model):
    """
    Модель для хранения причин аварий в зонах ≥3 БС
    """
    # Идентификатор группы (уникальный для каждой зоны аварии)
    group_id = models.CharField(max_length=255, verbose_name="ID группы", db_index=True)

    # Причина аварии на разных языках  
    reason = models.TextField(verbose_name="Причина аварии", null=True, blank=True)
    reason_ru = models.TextField(verbose_name="Причина аварии (рус)", null=True, blank=True)
    reason_en = models.TextField(verbose_name="Причина аварии (англ)", null=True, blank=True)

    # Информация о местоположении
    area_name = models.CharField(max_length=100, verbose_name="Название района", null=True, blank=True)
    region_name = models.CharField(max_length=100, verbose_name="Название региона", null=True, blank=True)

    # Количество затронутых станций
    stations_count = models.IntegerField(verbose_name="Количество станций")

    # JSON данные о станциях (TEXT для MySQL совместимости)
    stations_data = models.TextField(verbose_name="Данные станций", null=True, blank=True)

    # Временные метки
    incident_timestamp = models.DateTimeField(verbose_name="Время инцидента")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Дата создания")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Дата обновления")

    # Пользователь, добавивший причину
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="Создано пользователем")

    # Автоматически созданная запись (без причины)
    auto_created = models.BooleanField(default=False, verbose_name="Автоматически создано")

    class Meta:
        db_table = 'outage_reasons'
        verbose_name = "Причина аварии"
        verbose_name_plural = "Причины аварий"
        ordering = ['-incident_timestamp']
        indexes = [
            models.Index(fields=['group_id']),
            models.Index(fields=['incident_timestamp']),
            models.Index(fields=['area_name']),
            models.Index(fields=['region_name']),
        ]

    def __str__(self):
        return f"Авария в {self.area_name} ({self.stations_count} БС) - {self.incident_timestamp.strftime('%d.%m.%Y %H:%M')}"

    def get_location_display(self):
        """Возвращает местоположение в удобном формате"""
        if self.area_name and self.region_name:
            return f"{self.area_name}, {self.region_name}"
        elif self.area_name:
            return self.area_name
        elif self.region_name:
            return self.region_name
        return "Неизвестное местоположение"
    
    def get_reason(self, lang='ru'):
        """Возвращает причину на нужном языке"""
        if lang == 'en' and self.reason_en:
            return self.reason_en
        elif lang == 'ru' and self.reason_ru:
            return self.reason_ru
        # Fallback на старое поле reason
        return self.reason or ''
    
    def set_reason(self, reason_text, lang='ru'):
        """Устанавливает причину на нужном языке"""
        if lang == 'en':
            self.reason_en = reason_text
        else:
            self.reason_ru = reason_text
        # Также сохраняем в старое поле для обратной совместимости
        self.reason = reason_text
